# AdMesh Platform - Complete Environment Setup Guide

This repository contains the complete AdMesh platform with multiple components. This guide provides comprehensive instructions for running the application in development, test, and production environments.

## 🏗️ Repository Structure

```
admesh-protocol/
├── admesh-dashboard/     # Next.js Frontend Dashboard
├── admesh-protocol/      # FastAPI Backend API
├── admesh-docs/         # Documentation Site
├── admesh-ui-sdk/       # React UI SDK
├── admesh-python/       # Python SDK
├── admesh-typescript/   # TypeScript SDK
└── admesh-extension/    # Browser Extension
```

## 🌍 Environment Overview

| Environment | Purpose | Backend API | Frontend | Database |
|-------------|---------|-------------|----------|----------|
| **Development** | Local development | `http://127.0.0.1:8000` | `http://localhost:3000` | Development Firebase |
| **Test** | Testing with production data | `http://127.0.0.1:8000` | `http://localhost:3000` | Production Firebase |
| **Production** | Live deployment | `https://api.useadmesh.com` | `https://www.useadmesh.com` | Production Firebase |

---

## 🚀 Quick Start

### Prerequisites

- **Node.js** 18+ and npm
- **Python** 3.9+
- **Git**

### 1. Clone Repository
```bash
git clone <repository-url>
cd admesh-protocol
```

### 2. Choose Your Environment

#### For Development (Recommended for new developers)
```bash
# Backend
cd admesh-protocol
./run_dev.sh

# Frontend (in new terminal)
cd admesh-dashboard  
npm install
npm run dev
```

#### For Testing with Production Data
```bash
# Backend
cd admesh-protocol
./run_test.sh

# Frontend (in new terminal)
cd admesh-dashboard
npm install
./run_test.sh
```

#### For Production
```bash
# Backend
cd admesh-protocol
./run_prod.sh

# Frontend (in new terminal)
cd admesh-dashboard
npm install
npm run build
npm start
```

---

## 🔧 Detailed Setup Instructions

### Backend Setup (admesh-protocol)

#### 1. Environment Setup
```bash
cd admesh-protocol

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

#### 2. Firebase Configuration

**For Development:**
- Place `dev-serviceAccountKey.json` in `firebase/` directory
- Uses `admesh-dev` Firebase project

**For Test/Production:**
- Place `serviceAccountKey.json` in `firebase/` directory  
- Uses `admesh-9560c` Firebase project

#### 3. Environment Variables

The backend uses environment-specific `.env` files:
- `.env.dev` - Development configuration
- `.env.test` - Test configuration  
- `.env.production` - Production configuration

**Required Environment Variables:**
```bash
# External API Keys (required for all environments)
OPENROUTER_API_KEY=your_openrouter_key
RESEND_API_KEY=your_resend_key

# Optional: OpenAI API Key
OPENAI_API_KEY=your_openai_key
```

#### 4. Running Backend

**Development Mode:**
```bash
./run_dev.sh
# OR manually:
python scripts/switch_env.py dev
uvicorn api.main:app --reload --host 127.0.0.1 --port 8000
```

**Test Mode:**
```bash
./run_test.sh
# OR manually:
export ENV=test
uvicorn api.main:app --host 127.0.0.1 --port 8000 --reload --env-file .env.test
```

**Production Mode:**
```bash
./run_prod.sh
# OR manually:
python scripts/switch_env.py prod
uvicorn api.main:app --host 127.0.0.1 --port 8000
```

### Frontend Setup (admesh-dashboard)

#### 1. Dependencies
```bash
cd admesh-dashboard
npm install
```

#### 2. Environment Configuration

The frontend automatically detects environment based on `NEXT_PUBLIC_ENVIRONMENT`:

**Environment Files:**
- `.env.development` - Development configuration
- `.env.test` - Test configuration
- `.env.production` - Production configuration

#### 3. Running Frontend

**Development Mode:**
```bash
npm run dev
# Runs on http://localhost:3000
```

**Test Mode:**
```bash
npm run dev:test
# OR use the script:
./run_test.sh
```

**Production Mode:**
```bash
npm run build
npm start
# OR for development with production config:
npm run dev:prod
```

---

## 📋 Environment-Specific Details

### Development Environment

**Purpose:** Local development with full debugging

**Configuration:**
- **Backend:** Uses development Firebase project (`admesh-dev`)
- **Frontend:** Calls localhost API (`http://127.0.0.1:8000`)
- **Debug:** Enabled with detailed logging
- **CORS:** Permissive (allows all origins)

**URLs:**
- Backend API: `http://127.0.0.1:8000`
- Frontend: `http://localhost:3000`
- API Docs: `http://127.0.0.1:8000/docs`

### Test Environment

**Purpose:** Testing with production data on localhost

**Configuration:**
- **Backend:** Uses production Firebase project (`admesh-9560c`)
- **Frontend:** Uses production Firebase but calls localhost API
- **Debug:** Enabled for troubleshooting
- **Data:** Real production data for realistic testing

**URLs:**
- Backend API: `http://127.0.0.1:8000`
- Frontend: `http://localhost:3000`
- Database: Production Firestore

**⚠️ Security Note:** This environment accesses production data. Use responsibly.

### Production Environment

**Purpose:** Live deployment

**Configuration:**
- **Backend:** Production Firebase project (`admesh-9560c`)
- **Frontend:** Production Firebase and API
- **Debug:** Disabled for performance
- **CORS:** Restricted to production domains

**URLs:**
- Backend API: `https://api.useadmesh.com`
- Frontend: `https://www.useadmesh.com`
- Database: Production Firestore

---

## 🛠️ Development Workflow

### 1. Starting Development
```bash
# Terminal 1: Backend
cd admesh-protocol
./run_dev.sh

# Terminal 2: Frontend  
cd admesh-dashboard
npm run dev
```

### 2. Testing Changes
```bash
# Test with production data
cd admesh-protocol
./run_test.sh

# In another terminal
cd admesh-dashboard
./run_test.sh
```

### 3. Building for Production
```bash
# Backend
cd admesh-protocol
./run_prod.sh

# Frontend
cd admesh-dashboard
npm run build:prod
npm start
```

---

## 🔍 Troubleshooting

### Common Issues

#### Backend Won't Start
```bash
# Check environment
grep "ENV=" admesh-protocol/.env

# Verify Firebase credentials
ls -la admesh-protocol/firebase/

# Check dependencies
cd admesh-protocol
pip install -r requirements.txt
```

#### Frontend API Connection Failed
```bash
# Verify backend is running
curl http://127.0.0.1:8000/health

# Check environment variables
cd admesh-dashboard
grep "NEXT_PUBLIC_API_BASE_URL" .env.*
```

#### Environment Variables Missing
```bash
# Backend: Check required variables
cd admesh-protocol
python -c "from config.config_manager import get_config; print(get_config().get_required_env_vars())"

# Frontend: Check environment detection
cd admesh-dashboard
npm run dev -- --debug
```

### Port Conflicts
```bash
# Check what's using port 8000
lsof -i :8000

# Kill process if needed
kill -9 <PID>
```

### Firebase Connection Issues
```bash
# Verify credentials file exists and is valid
cd admesh-protocol
python -c "import firebase_admin; print('Firebase credentials valid')"
```

---

## 📚 Additional Resources

- **API Documentation:** `http://127.0.0.1:8000/docs` (when backend is running)
- **Environment Configuration:** `admesh-protocol/ENVIRONMENT_GUIDE.md`
- **Test Environment Details:** `admesh-protocol/TEST_ENVIRONMENT_SETUP.md`
- **Frontend Configuration:** `admesh-dashboard/src/config/environment.ts`

---

## 🤝 Getting Help

If you encounter issues:

1. **Check the logs** in your terminal for error messages
2. **Verify environment variables** are set correctly
3. **Ensure all dependencies** are installed
4. **Check Firebase credentials** are in the correct location
5. **Verify ports** 3000 and 8000 are available

For additional support, refer to the specific component documentation in each directory.

---

## 📦 Package Management

### Backend Dependencies (Python)

**Installing Dependencies:**
```bash
cd admesh-protocol
pip install -r requirements.txt
```

**Adding New Dependencies:**
```bash
# Install new package
pip install package-name

# Update requirements.txt
pip freeze > requirements.txt
```

**Virtual Environment Management:**
```bash
# Create virtual environment
python -m venv venv

# Activate (Linux/Mac)
source venv/bin/activate

# Activate (Windows)
venv\Scripts\activate

# Deactivate
deactivate
```

### Frontend Dependencies (Node.js)

**Installing Dependencies:**
```bash
cd admesh-dashboard
npm install
```

**Adding New Dependencies:**
```bash
# Production dependency
npm install package-name

# Development dependency
npm install --save-dev package-name

# Update all dependencies
npm update
```

**Package Scripts:**
```bash
# Development
npm run dev              # Start development server
npm run dev:test         # Start with test environment
npm run dev:prod         # Start with production config

# Building
npm run build            # Build for production
npm run build:test       # Build for test environment
npm run build:prod       # Build for production

# Other
npm run lint             # Run ESLint
npm start                # Start production server
```

---

## ⚙️ Advanced Configuration

### Backend Configuration Files

**Configuration Structure:**
```
admesh-protocol/config/
├── base.py              # Base configuration class
├── development.py       # Development settings
├── test.py             # Test environment settings
├── production.py       # Production settings
└── config_manager.py   # Configuration manager
```

**Environment Switching:**
```bash
# Switch environments programmatically
python scripts/switch_env.py dev
python scripts/switch_env.py test
python scripts/switch_env.py prod

# Check current environment
python -c "from config.config_manager import get_environment; print(get_environment())"
```

### Frontend Configuration

**Environment Detection Logic:**
1. `NEXT_PUBLIC_ENVIRONMENT` environment variable
2. `NODE_ENV` for fallback
3. Vercel environment detection
4. Default to development

**Configuration File:** `admesh-dashboard/src/config/environment.ts`

### Firebase Configuration

**Development Setup:**
```bash
# Place development credentials
cp dev-serviceAccountKey.json admesh-protocol/firebase/

# Verify configuration
python -c "
from config.config_manager import get_config
config = get_config()
print(f'Firebase Project: {config.firebase_config[\"project_id\"]}')
"
```

**Production Setup:**
```bash
# Place production credentials
cp serviceAccountKey.json admesh-protocol/firebase/

# For Cloud Run deployment, credentials are handled via secrets
```

---

## 🔄 Deployment

### Local Development Deployment

**Full Stack Development:**
```bash
# Terminal 1: Backend
cd admesh-protocol
./run_dev.sh

# Terminal 2: Frontend
cd admesh-dashboard
npm run dev

# Terminal 3: Documentation (optional)
cd admesh-docs
npm run start
```

### Test Environment Deployment

**Testing with Production Data:**
```bash
# Terminal 1: Backend (production DB, localhost API)
cd admesh-protocol
./run_test.sh

# Terminal 2: Frontend (production Firebase, localhost API)
cd admesh-dashboard
./run_test.sh
```

### Production Deployment

**Backend (Google Cloud Run):**
```bash
cd admesh-protocol

# Build and deploy
gcloud run deploy admesh-api \
  --source . \
  --platform managed \
  --region us-central1 \
  --project admesh-9560c
```

**Frontend (Vercel):**
```bash
cd admesh-dashboard

# Build for production
npm run build:prod

# Deploy to Vercel
vercel --prod
```

---

## 🧪 Testing

### Backend Testing

**API Testing:**
```bash
cd admesh-protocol

# Test health endpoint
curl http://127.0.0.1:8000/health

# Test with authentication
curl -H "Authorization: Bearer your-token" \
     http://127.0.0.1:8000/api/recommendations
```

**Unit Tests:**
```bash
cd admesh-protocol
python -m pytest tests/
```

### Frontend Testing

**Development Testing:**
```bash
cd admesh-dashboard

# Run linting
npm run lint

# Test build
npm run build

# Integration testing
npm run test-integration
```

### End-to-End Testing

**Full Stack Testing:**
```bash
# Start backend in test mode
cd admesh-protocol && ./run_test.sh &

# Start frontend in test mode
cd admesh-dashboard && ./run_test.sh &

# Run integration tests
cd admesh-dashboard && npm run test-integration
```

---

## 📊 Monitoring and Logging

### Backend Logging

**Log Levels by Environment:**
- **Development:** DEBUG (all logs)
- **Test:** DEBUG (for troubleshooting)
- **Production:** WARNING (errors and warnings only)

**Viewing Logs:**
```bash
# Development logs (verbose)
cd admesh-protocol
./run_dev.sh

# Production logs (filtered)
cd admesh-protocol
./run_prod.sh
```

### Frontend Logging

**Environment-Specific Logging:**
```javascript
// In development/test
console.log('Debug information')

// In production (disabled)
// Logs are automatically filtered
```

**Error Monitoring:**
- Development: Console logging
- Test: Console + debugging enabled
- Production: Error reporting enabled

---

## 🔐 Security Considerations

### Environment Variables

**Sensitive Data:**
- Never commit `.env` files to version control
- Use environment-specific files (`.env.dev`, `.env.test`, `.env.production`)
- Store production secrets in secure services (Google Secret Manager, Vercel Environment Variables)

**API Keys:**
```bash
# Required for all environments
OPENROUTER_API_KEY=sk-or-v1-...
RESEND_API_KEY=re_...

# Optional but recommended
OPENAI_API_KEY=sk-...
STRIPE_SECRET_KEY=sk_...
```

### Firebase Security

**Development:**
- Uses separate Firebase project (`admesh-dev`)
- Limited access and test data

**Production:**
- Uses production Firebase project (`admesh-9560c`)
- Full security rules and real data
- Credentials managed via Cloud Run secrets

### CORS Configuration

**Development:** Permissive (allows all origins)
**Test:** Allows localhost and production domains
**Production:** Restricted to production domains only

---

## 🚨 Emergency Procedures

### Quick Recovery

**Backend Issues:**
```bash
# Reset environment
cd admesh-protocol
python scripts/switch_env.py dev
./run_dev.sh
```

**Frontend Issues:**
```bash
# Clear cache and restart
cd admesh-dashboard
rm -rf .next node_modules
npm install
npm run dev
```

**Database Connection Issues:**
```bash
# Verify Firebase credentials
cd admesh-protocol
python -c "
import firebase_admin
from firebase_admin import credentials
cred = credentials.Certificate('./firebase/serviceAccountKey.json')
print('Firebase credentials valid')
"
```

### Rollback Procedures

**Environment Rollback:**
```bash
# Switch back to working environment
cd admesh-protocol
python scripts/switch_env.py dev

cd admesh-dashboard
npm run dev
```

**Dependency Rollback:**
```bash
# Backend
cd admesh-protocol
pip install -r requirements.txt --force-reinstall

# Frontend
cd admesh-dashboard
rm -rf node_modules package-lock.json
npm install
```
