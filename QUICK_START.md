# AdMesh - Quick Start Reference

## 🚀 One-Command Setup

### Development (Most Common)
```bash
# Backend
cd admesh-protocol && ./run_dev.sh

# Frontend (new terminal)
cd admesh-dashboard && npm install && npm run dev
```

### Test Environment (Production Data)
```bash
# Backend
cd admesh-protocol && ./run_test.sh

# Frontend (new terminal)  
cd admesh-dashboard && ./run_test.sh
```

### Production
```bash
# Backend
cd admesh-protocol && ./run_prod.sh

# Frontend (new terminal)
cd admesh-dashboard && npm run build && npm start
```

---

## 📋 Environment Quick Reference

| Environment | Backend | Frontend | Database | Purpose |
|-------------|---------|----------|----------|---------|
| **dev** | `127.0.0.1:8000` | `localhost:3000` | admesh-dev | Local development |
| **test** | `127.0.0.1:8000` | `localhost:3000` | admesh-9560c | Test with prod data |
| **prod** | `api.useadmesh.com` | `www.useadmesh.com` | admesh-9560c | Live deployment |

---

## 🔧 Essential Commands

### Backend (admesh-protocol)
```bash
# Quick start
./run_dev.sh              # Development
./run_test.sh             # Test environment  
./run_prod.sh             # Production

# Manual control
python scripts/switch_env.py dev
uvicorn api.main:app --reload --host 127.0.0.1 --port 8000

# Dependencies
pip install -r requirements.txt
source venv/bin/activate
```

### Frontend (admesh-dashboard)
```bash
# Development
npm run dev               # Development mode
npm run dev:test          # Test environment
npm run dev:prod          # Production config

# Building
npm run build             # Production build
npm run build:test        # Test build

# Dependencies
npm install               # Install packages
npm update                # Update packages
```

---

## 🔍 Health Checks

### Verify Backend
```bash
curl http://127.0.0.1:8000/health
curl http://127.0.0.1:8000/docs
```

### Verify Frontend
```bash
# Check if running
curl http://localhost:3000

# Check environment
grep NEXT_PUBLIC_ENVIRONMENT admesh-dashboard/.env*
```

### Verify Database Connection
```bash
cd admesh-protocol
python -c "
from config.config_manager import get_config
config = get_config()
print(f'Environment: {config.environment}')
print(f'Firebase Project: {config.firebase_config[\"project_id\"]}')
"
```

---

## 🚨 Troubleshooting

### Port Already in Use
```bash
# Find process using port
lsof -i :8000
lsof -i :3000

# Kill process
kill -9 <PID>
```

### Environment Issues
```bash
# Check current environment
cd admesh-protocol
grep "ENV=" .env

# Reset environment
python scripts/switch_env.py dev
```

### Dependencies Issues
```bash
# Backend
cd admesh-protocol
pip install -r requirements.txt --force-reinstall

# Frontend
cd admesh-dashboard
rm -rf node_modules package-lock.json
npm install
```

### Firebase Connection
```bash
# Check credentials exist
ls -la admesh-protocol/firebase/

# Verify credentials
cd admesh-protocol
python -c "import firebase_admin; print('Firebase OK')"
```

---

## 📁 Required Files

### Backend
```
admesh-protocol/
├── firebase/
│   ├── dev-serviceAccountKey.json      # Development
│   └── serviceAccountKey.json          # Test/Production
├── .env.dev                            # Development config
├── .env.test                           # Test config
└── .env.production                     # Production config
```

### Frontend
```
admesh-dashboard/
├── .env.development                    # Development config
├── .env.test                          # Test config
└── .env.production                    # Production config
```

---

## 🔑 Required Environment Variables

### Backend (.env files)
```bash
# Required for all environments
OPENROUTER_API_KEY=sk-or-v1-...
RESEND_API_KEY=re_...

# Optional
OPENAI_API_KEY=sk-...
STRIPE_SECRET_KEY=sk_...
```

### Frontend (.env files)
```bash
# Automatically configured per environment
NEXT_PUBLIC_ENVIRONMENT=development|test|production
NEXT_PUBLIC_API_BASE_URL=http://127.0.0.1:8000
NEXT_PUBLIC_FIREBASE_PROJECT_ID=admesh-dev|admesh-9560c
```

---

## 🎯 Development Workflow

### 1. Start Development
```bash
# Terminal 1
cd admesh-protocol && ./run_dev.sh

# Terminal 2  
cd admesh-dashboard && npm run dev
```

### 2. Make Changes
- Backend: Edit files in `admesh-protocol/`
- Frontend: Edit files in `admesh-dashboard/src/`
- Auto-reload is enabled in development

### 3. Test Changes
```bash
# Switch to test environment
cd admesh-protocol && ./run_test.sh
cd admesh-dashboard && ./run_test.sh
```

### 4. Build for Production
```bash
cd admesh-dashboard && npm run build:prod
cd admesh-protocol && ./run_prod.sh
```

---

## 📚 Useful URLs

### Development
- Backend API: http://127.0.0.1:8000
- API Docs: http://127.0.0.1:8000/docs
- Frontend: http://localhost:3000

### Production
- Backend API: https://api.useadmesh.com
- Frontend: https://www.useadmesh.com
- Documentation: https://docs.useadmesh.com

---

## 🆘 Getting Help

### Check Logs
```bash
# Backend logs are shown in terminal
cd admesh-protocol && ./run_dev.sh

# Frontend logs in browser console
# Open DevTools → Console
```

### Common Solutions
1. **Port conflicts**: Kill processes using ports 3000/8000
2. **Environment issues**: Run `python scripts/switch_env.py dev`
3. **Dependencies**: Reinstall with `pip install -r requirements.txt` and `npm install`
4. **Firebase**: Verify credentials files exist in `firebase/` directory
5. **API connection**: Ensure backend is running before starting frontend

### Emergency Reset
```bash
# Complete reset
cd admesh-protocol
python scripts/switch_env.py dev
pip install -r requirements.txt
./run_dev.sh

# In new terminal
cd admesh-dashboard
rm -rf .next node_modules
npm install
npm run dev
```

---

## 📖 Full Documentation

For complete setup instructions, see: `ENVIRONMENT_SETUP_GUIDE.md`
