"use client";

import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, ArrowRight, Mail, CheckCircle, AlertCircle, X } from "lucide-react";
import { sendEmailVerification, onAuthStateChanged } from "firebase/auth";
import { useAuth } from "@/hooks/use-auth";
import { validateEmailDomainMatch, getDomainValidationError, generateBrandSlug, generateProductSlug } from "@/lib/utils";
import { auth } from "@/lib/firebase";
import FormField from "../ui/FormField";
import FormSection from "../ui/FormSection";
import AnimatedContainer from "../ui/AnimatedContainer";
import { Brand } from "@/types/onboarding";


interface BrandStepProps {
  brand: {
    website: string;
    brand_name: string;
    logo_url: string;
    work_email: string;
    headquarters: string;
    application_type: string;
  };
  setBrand: (brand: Brand) => void;
  errors: Record<string, string>;
  onNext: () => void;
  loading: boolean;
  userEmail: string;
  onFetchInfo: (website: string) => Promise<boolean>;
}

const BrandStep = ({ brand, setBrand, errors, onNext, loading, userEmail, onFetchInfo }: BrandStepProps) => {
  const { user, refreshUser } = useAuth();
  const [showAllFields, setShowAllFields] = useState(false);
  const [fetchingDomainInfo, setFetchingDomainInfo] = useState(false);
  const [domainFetchError, setDomainFetchError] = useState("");
  const [sendingVerification, setSendingVerification] = useState(false);
  const [emailVerificationSent, setEmailVerificationSent] = useState(false);
  const [hasAttemptedAutoPopulation, setHasAttemptedAutoPopulation] = useState(false);
  const [fieldValidationStates, setFieldValidationStates] = useState({
    website: false,
    brand_name: false,
    work_email: false,
    headquarters: false,
    logo_url: false,
    application_type: false
  });


  // Check if brand data is pre-filled (indicating auto-fill from existing data)
  const isDataPreFilled = brand.website || brand.brand_name || brand.work_email;

  // Auto-show fields if data is pre-filled
  useEffect(() => {
    if (isDataPreFilled && !showAllFields) {
      console.log("BrandStep: Auto-showing fields due to pre-filled data");
      setShowAllFields(true);
      setHasAttemptedAutoPopulation(true);
    }
  }, [isDataPreFilled, showAllFields]);

  // Pure validation function without state updates
  const isFieldValid = useCallback((fieldName: string, value: string) => {
    switch (fieldName) {
      case 'website':
        return !!value && value.match(/^(https?:\/\/)?(www\.)?[a-zA-Z0-9][a-zA-Z0-9-]*\.[a-zA-Z0-9-\.]+/) !== null;
      case 'brand_name':
        return !!value && value.trim().length > 0;
      case 'work_email':
        return !!value && value.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/) !== null;
      case 'application_type':
        return !!value && value.trim().length > 0;
      case 'headquarters':
      case 'logo_url':
        // Optional fields are always valid
        return true;
      default:
        return false;
    }
  }, []);

  // Validate field and update state
  const validateField = useCallback((fieldName: string, value: string) => {
    const isValid = isFieldValid(fieldName, value);
    setFieldValidationStates(prev => ({
      ...prev,
      [fieldName]: isValid
    }));
    return isValid;
  }, [isFieldValid]);

  // Validate all required fields without updating state
  const validateAllFields = useCallback(() => {
    const requiredFields = ['website', 'brand_name', 'work_email', 'application_type'];
    return requiredFields.every(field => isFieldValid(field, brand[field as keyof typeof brand] || ''));
  }, [brand, isFieldValid]);

  // Update validation states when brand data changes
  useEffect(() => {
    if (showAllFields) {
      const newValidationStates = {
        website: isFieldValid('website', brand.website || ''),
        brand_name: isFieldValid('brand_name', brand.brand_name || ''),
        work_email: isFieldValid('work_email', brand.work_email || ''),
        headquarters: isFieldValid('headquarters', brand.headquarters || ''),
        logo_url: isFieldValid('logo_url', brand.logo_url || ''),
        application_type: isFieldValid('application_type', brand.application_type || '')
      };
      setFieldValidationStates(newValidationStates);
    }
  }, [brand, showAllFields, isFieldValid]);

  // Check if user email domain matches brand domain
  const emailMatchesDomain = validateEmailDomainMatch(userEmail, brand.website);
  const domainValidationError = getDomainValidationError(userEmail, brand.website);

  // Check if email verification is required and if user's email is verified
  const requiresEmailVerification = brand.website && !emailMatchesDomain;
  const isEmailVerified = user?.emailVerified || false;

  // Real-time email verification monitoring
  useEffect(() => {
    if (!user) return;

    const unsubscribe = onAuthStateChanged(auth, async (currentUser) => {
      if (currentUser) {
        // Force refresh to get updated emailVerified status
        await currentUser.reload();
        refreshUser();
      }
    });

    return () => unsubscribe();
  }, [user, refreshUser]);

  // Calculate completion percentage for validation summary
  const getCompletionPercentage = useCallback(() => {
    const requiredFields = ['website', 'brand_name', 'work_email', 'application_type'];
    const completedFields = requiredFields.filter(field => fieldValidationStates[field as keyof typeof fieldValidationStates]);
    const emailVerificationComplete = !requiresEmailVerification || isEmailVerified;

    let totalRequirements = requiredFields.length;
    let completedRequirements = completedFields.length;

    if (requiresEmailVerification) {
      totalRequirements += 1;
      if (emailVerificationComplete) {
        completedRequirements += 1;
      }
    }

    return Math.round((completedRequirements / totalRequirements) * 100);
  }, [fieldValidationStates, requiresEmailVerification, isEmailVerified]);

  // Check if all requirements are met
  const allRequirementsMet = useCallback(() => {
    const fieldsValid = validateAllFields();
    const emailVerificationComplete = !requiresEmailVerification || isEmailVerified;
    return fieldsValid && emailVerificationComplete && showAllFields;
  }, [validateAllFields, requiresEmailVerification, isEmailVerified, showAllFields]);

  const handleSendEmailVerification = async () => {
    if (!user) {
      toast.error("User not found");
      return;
    }

    // Check domain validation first
    if (domainValidationError) {
      toast.error(domainValidationError);
      return;
    }

    setSendingVerification(true);
    try {
      await sendEmailVerification(user);
      setEmailVerificationSent(true);
      toast.success(
        "Verification email sent! Please check your inbox and click the link to verify your email.",
        {
          action: {
            label: "Refresh Status",
            onClick: () => {
              refreshUser();
              // Check verification status after a short delay
              setTimeout(() => {
                refreshUser();
              }, 2000);
            }
          },
          duration: 10000
        }
      );
    } catch (error) {
      console.error("Error sending verification email:", error);
      toast.error("Failed to send verification email. Please try again later.");
    } finally {
      setSendingVerification(false);
    }
  };

  const handleFetchInfo = async () => {
    if (!brand.website) {
      toast.error("Please enter a website");
      return;
    }
    setFetchingDomainInfo(true);
    setDomainFetchError("");

    try {
      // Use the parent component's fetchWebsiteInfo function
      const success = await onFetchInfo(brand.website);

      if (success) {
        setShowAllFields(true);
      } else {
        setDomainFetchError("Could not fetch information for this website. Please fill in the details manually.");
        setShowAllFields(true);
      }
    } catch (err) {
      console.error("❌ Error fetching website info:", err);
      setDomainFetchError("Could not fetch information for this website. Please fill in the details manually.");
      setShowAllFields(true);
    } finally {
      setFetchingDomainInfo(false);
    }
  };

  const handleContinue = async () => {
    // If fields aren't shown yet, show them first
    if (!showAllFields) {
      setShowAllFields(true);
      return; // Don't proceed with validation yet
    }

    // Validate all fields first
    const fieldsValid = validateAllFields();
    if (!fieldsValid) {
      toast.error("Please fill in all required fields correctly.");
      return;
    }

    // Check if email verification is required but not completed
    if (requiresEmailVerification && !isEmailVerified) {
      toast.error("Please verify your email before continuing. Check your inbox for the verification link.");
      return;
    }

    // Check domain validation
    if (domainValidationError) {
      toast.error(domainValidationError);
      return;
    }

    // Generate and set slugs before proceeding
    const brandSlug = generateBrandSlug(brand.website);
    const productSlug = generateProductSlug(brand.brand_name);

    // Update brand with generated slugs (this will be used by the parent component)
    setBrand({
      ...brand,
      brand_slug: brandSlug,
      product_slug: productSlug
    });

    onNext();
  };

  return (
    <AnimatedContainer dataStep={1}>
      <FormSection
        title="Let's start with your brand"
        description="Tell us about who you are and how we can reach you."
      >
        <div className="grid gap-6 w-full">
          {/* Validation Summary Component */}
          {showAllFields && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="p-4 rounded-lg border bg-muted/30"
            >
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium">Setup Progress</h3>
                <span className="text-sm text-muted-foreground">{getCompletionPercentage()}% Complete</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2 mb-3">
                <motion.div
                  className="bg-primary h-2 rounded-full transition-all duration-300"
                  initial={{ width: 0 }}
                  animate={{ width: `${getCompletionPercentage()}%` }}
                />
              </div>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="flex items-center gap-1">
                  {fieldValidationStates.website ? (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  ) : (
                    <AlertCircle className="h-3 w-3 text-orange-500" />
                  )}
                  <span>Website</span>
                </div>
                <div className="flex items-center gap-1">
                  {fieldValidationStates.brand_name ? (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  ) : (
                    <AlertCircle className="h-3 w-3 text-orange-500" />
                  )}
                  <span>Brand Name</span>
                </div>
                <div className="flex items-center gap-1">
                  {fieldValidationStates.work_email ? (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  ) : (
                    <AlertCircle className="h-3 w-3 text-orange-500" />
                  )}
                  <span>Work Email</span>
                </div>
                <div className="flex items-center gap-1">
                  {fieldValidationStates.application_type ? (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  ) : (
                    <AlertCircle className="h-3 w-3 text-orange-500" />
                  )}
                  <span>App Type</span>
                </div>
                {requiresEmailVerification && (
                  <div className="flex items-center gap-1 col-span-2">
                    {isEmailVerified ? (
                      <CheckCircle className="h-3 w-3 text-green-500" />
                    ) : (
                      <AlertCircle className="h-3 w-3 text-orange-500" />
                    )}
                    <span>Email Verification</span>
                  </div>
                )}
              </div>
            </motion.div>
          )}

          <FormField>
            <div className="flex items-center gap-2 mb-2">
              <Label htmlFor="website" className="text-sm font-medium">Website</Label>
              {showAllFields && (
                fieldValidationStates.website ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-orange-500" />
                )
              )}
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <div className="w-full">
                <Input
                  id="website"
                  placeholder="yourbrand.com"
                  value={brand.website}
                  onChange={(e) => setBrand({ ...brand, website: e.target.value })}
                  className={`transition-all duration-200 focus:ring-2 focus:ring-primary/20 ${
                    errors.website ? 'border-red-500' :
                    fieldValidationStates.website ? 'border-green-500 bg-green-50' :
                    isDataPreFilled && brand.website ? 'bg-green-50 border-green-200' : ''
                  }`}
                />
                {errors.website && (
                  <p className="text-xs text-red-500 mt-1">{errors.website}</p>
                )}
                {isDataPreFilled && brand.website && !errors.website && (
                  <p className="text-xs text-green-600 mt-1">✓ Auto-filled from your account</p>
                )}
              </div>
              <Button
                onClick={handleFetchInfo}
                disabled={fetchingDomainInfo || loading}
                variant="outline"
                className="whitespace-nowrap w-full sm:w-auto"
              >
                {fetchingDomainInfo || loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {loading ? "Auto-fetching..." : "Fetching..."}
                  </>
                ) : (
                  <>
                    <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M12 7V12L15 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    Fetch Info
                  </>
                )}
              </Button>
            </div>
            {domainFetchError && (
              <p className="text-xs text-red-500 mt-1">{domainFetchError}</p>
            )}
          </FormField>

          {/* Email Verification Section */}
          {brand.website && (
            <FormField>
              <Label className="text-sm font-medium">Email Verification</Label>
              <div className="space-y-3">
                <div className="flex items-center gap-2 p-3 rounded-lg border bg-muted/30">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">Your email:</span>
                  <span className="text-sm font-medium">{userEmail}</span>
                  {isEmailVerified ? (
                    <CheckCircle className="h-4 w-4 text-green-500 ml-auto" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-orange-500 ml-auto" />
                  )}
                </div>

                {emailMatchesDomain ? (
                  <div className="flex items-center gap-2 text-sm text-green-600 dark:text-green-400">
                    <CheckCircle className="h-4 w-4" />
                    <span>Email domain matches your website domain</span>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm text-orange-600 dark:text-orange-400">
                      <AlertCircle className="h-4 w-4" />
                      <span>Email verification required for domain validation</span>
                    </div>
                    {domainValidationError && (
                      <p className="text-xs text-red-500">{domainValidationError}</p>
                    )}
                    {!isEmailVerified && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={handleSendEmailVerification}
                        disabled={sendingVerification || !!domainValidationError}
                        className="w-full sm:w-auto"
                      >
                        {sendingVerification ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Sending...
                          </>
                        ) : (
                          <>
                            <Mail className="mr-2 h-4 w-4" />
                            {emailVerificationSent ? "Resend Verification Email" : "Send Verification Email"}
                          </>
                        )}
                      </Button>
                    )}
                    {isEmailVerified && (
                      <div className="flex items-center gap-2 text-sm text-green-600 dark:text-green-400">
                        <CheckCircle className="h-4 w-4" />
                        <span>Email verified successfully</span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </FormField>
          )}

          {showAllFields && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              transition={{ duration: 0.3 }}
              className="space-y-6"
            >
              <FormField>
                <div className="flex items-center gap-2 mb-2">
                  <Label htmlFor="brand_name" className="text-sm font-medium">Brand Name</Label>
                  {fieldValidationStates.brand_name ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-orange-500" />
                  )}
                </div>
                <Input
                  id="brand_name"
                  placeholder="Your Brand Name"
                  value={brand.brand_name}
                  onChange={(e) => setBrand({ ...brand, brand_name: e.target.value })}
                  className={`transition-all duration-200 focus:ring-2 focus:ring-primary/20 ${
                    errors.brand_name ? 'border-red-500' :
                    fieldValidationStates.brand_name ? 'border-green-500 bg-green-50' :
                    isDataPreFilled && brand.brand_name ? 'bg-green-50 border-green-200' : ''
                  }`}
                />
                {errors.brand_name && (
                  <p className="text-xs text-red-500 mt-1">{errors.brand_name}</p>
                )}
                {isDataPreFilled && brand.brand_name && !errors.brand_name && (
                  <p className="text-xs text-green-600 mt-1">✓ Auto-filled from website information</p>
                )}
              </FormField>

              <FormField>
                <div className="flex items-center gap-2 mb-2">
                  <Label htmlFor="application_type" className="text-sm font-medium">Application Type</Label>
                  {fieldValidationStates.application_type ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-orange-500" />
                  )}
                </div>
                <select
                  id="application_type"
                  value={brand.application_type}
                  onChange={(e) => setBrand({ ...brand, application_type: e.target.value })}
                  className={`w-full rounded-md border border-input bg-background p-2 focus:ring-2 focus:ring-primary/20 ${
                    errors.application_type ? 'border-red-500' :
                    fieldValidationStates.application_type ? 'border-green-500 bg-green-50' : ''
                  }`}
                >
                  <option value="">Select application type</option>
                  <option value="website">Website</option>
                  <option value="mobile_app">Mobile App</option>
                  <option value="desktop">Desktop Application</option>
                  <option value="both">Web & Mobile</option>
                  <option value="other">Other</option>
                </select>
                {errors.application_type ? (
                  <p className="text-xs text-red-500 mt-1">{errors.application_type}</p>
                ) : (
                  <p className="text-xs text-muted-foreground mt-1">
                    Select the platform where your product is available.
                  </p>
                )}
              </FormField>

              {/* Show work_email input ONLY if user's email doesn't match brand domain */}
              {!emailMatchesDomain && (
                <FormField>
                  <div className="flex items-center gap-2 mb-2">
                    <Label htmlFor="work_email" className="text-sm font-medium">Work Email</Label>
                    {fieldValidationStates.work_email ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-orange-500" />
                    )}
                  </div>
                  <div className="flex items-center w-full">
                    <Input
                      id="work_email_prefix"
                      type="text"
                      placeholder="you"
                      value={brand.work_email.split('@')[0] || ''}
                      onChange={(e) => {
                        const emailPrefix = e.target.value;
                        const cleanDomain = brand.website.replace(/^https?:\/\//, "").replace(/^www\./, "").split('/')[0];
                        setBrand({ ...brand, work_email: `${emailPrefix}@${cleanDomain}` });
                      }}
                      className={`w-2/5 sm:w-1/2 rounded-r-none transition-all duration-200 focus:ring-2 focus:ring-primary/20 ${
                        errors.work_email ? 'border-red-500' :
                        fieldValidationStates.work_email ? 'border-green-500 bg-green-50' : ''
                      }`}
                    />
                    <div className="w-3/5 sm:w-1/2 flex items-center bg-muted px-2 sm:px-3 py-2 border border-l-0 border-input rounded-r-md text-muted-foreground overflow-hidden text-ellipsis whitespace-nowrap text-xs sm:text-sm">
                      @{brand.website.replace(/^https?:\/\//, "").replace(/^www\./, "").split('/')[0]}
                    </div>
                  </div>
                  {errors.work_email ? (
                    <p className="text-xs text-red-500 mt-1">{errors.work_email}</p>
                  ) : (
                    <p className="text-xs text-muted-foreground mt-1">
                      We&apos;ll use this to verify your ownership of the domain.
                    </p>
                  )}
                </FormField>
              )}
            </motion.div>
          )}

          <motion.div
            className="flex justify-end pt-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
          >
            <motion.div
              whileHover={{ scale: allRequirementsMet() ? 1.03 : 1 }}
              whileTap={{ scale: allRequirementsMet() ? 0.97 : 1 }}
              className="w-full sm:w-auto relative"
            >
              <Button
                onClick={handleContinue}
                disabled={loading || (showAllFields && !allRequirementsMet())}
                className={`w-full sm:w-auto px-6 py-2 rounded-full relative overflow-hidden group shadow-md hover:shadow-lg transition-all duration-300 ${
                  showAllFields && !allRequirementsMet()
                    ? 'opacity-50 cursor-not-allowed'
                    : 'hover:shadow-lg'
                }`}
                title={
                  showAllFields && !allRequirementsMet()
                    ? `Complete all requirements (${getCompletionPercentage()}% done)`
                    : undefined
                }
              >
                <div className="relative z-10 flex items-center justify-center">
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      {!showAllFields ? "Continue" : "Next"}
                      <ArrowRight className="ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" />
                    </>
                  )}
                </div>
                <div className="absolute inset-0 bg-gradient-to-r from-primary to-blue-600 dark:from-primary dark:to-blue-500 opacity-90 group-hover:opacity-100 transition-all duration-300"></div>
              </Button>

              {/* Tooltip for disabled state */}
              {showAllFields && !allRequirementsMet() && (
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                  Complete all requirements to continue
                </div>
              )}
            </motion.div>
          </motion.div>
        </div>
      </FormSection>
    </AnimatedContainer>
  );
};

export default BrandStep;
