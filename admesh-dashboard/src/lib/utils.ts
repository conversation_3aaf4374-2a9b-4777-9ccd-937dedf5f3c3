import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Extract domain from a URL or email
 */
export function extractDomain(input: string): string {
  if (!input) return "";

  // If it's an email, extract domain after @
  if (input.includes("@")) {
    return input.split("@")[1].toLowerCase();
  }

  // If it's a URL, extract domain
  try {
    // Remove protocol if present
    let domain = input.replace(/^https?:\/\//, "");
    // Remove www. if present
    domain = domain.replace(/^www\./, "");
    // Remove path and query parameters
    domain = domain.split("/")[0].split("?")[0];
    return domain.toLowerCase();
  } catch {
    return input.toLowerCase();
  }
}

/**
 * Check if email domain matches website domain
 */
export function validateEmailDomainMatch(email: string, website: string): boolean {
  if (!email || !website) return false;

  const emailDomain = extractDomain(email);
  const websiteDomain = extractDomain(website);

  if (!emailDomain || !websiteDomain) return false;

  // Direct match
  if (emailDomain === websiteDomain) return true;

  // Check if email domain is a subdomain of website domain
  if (emailDomain.endsWith(`.${websiteDomain}`)) return true;

  // Check if website domain is a subdomain of email domain
  if (websiteDomain.endsWith(`.${emailDomain}`)) return true;

  return false;
}

/**
 * Get domain validation error message
 */
export function getDomainValidationError(email: string, website: string): string | null {
  if (!email || !website) return "Email and website are required";

  const emailDomain = extractDomain(email);
  const websiteDomain = extractDomain(website);

  if (!emailDomain) return "Invalid email format";
  if (!websiteDomain) return "Invalid website format";

  if (!validateEmailDomainMatch(email, website)) {
    return `Email domain (${emailDomain}) must match website domain (${websiteDomain})`;
  }

  return null;
}

/**
 * Extract website slug by parsing the domain between "www." and ".com"
 * For example: "www.example.com" -> "example"
 */
export function extractWebsiteSlug(website: string): string {
  if (!website) return "";

  try {
    // Normalize the URL
    let url = website.trim();
    if (!url.startsWith("http://") && !url.startsWith("https://")) {
      url = "https://" + url;
    }

    const domain = new URL(url).hostname.toLowerCase();

    // Remove www. prefix if present
    const cleanDomain = domain.startsWith("www.") ? domain.substring(4) : domain;

    // Split by dots and take the first part as the slug
    const parts = cleanDomain.split(".");
    if (parts.length >= 2) {
      return parts[0]; // Return the first part (e.g., "example" from "example.com")
    }

    return cleanDomain;
  } catch {
    return "";
  }
}

/**
 * Normalize website URL for consistent storage and comparison
 */
export function normalizeWebsiteUrl(website: string): string {
  if (!website) return "";

  try {
    let url = website.trim();
    if (!url.startsWith("http://") && !url.startsWith("https://")) {
      url = "https://" + url;
    }

    const urlObj = new URL(url);
    // Return normalized URL with protocol and hostname
    return `${urlObj.protocol}//${urlObj.hostname}`;
  } catch {
    return website;
  }
}

/**
 * Converts a dollar amount to cents for backend storage
 * @param amount - The amount in dollars (can be string or number)
 * @returns The amount in cents as an integer
 */
export function dollarsToCents(amount: string | number): number {
  // Handle empty strings or undefined
  if (amount === '' || amount === undefined || amount === null) {
    return 0;
  }

  // Convert to number if it's a string
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

  // Check if it's a valid number
  if (isNaN(numAmount)) {
    return 0;
  }

  // Convert to cents and round to avoid floating point issues
  return Math.round(numAmount * 100);
}

/**
 * Converts cents to dollars for display
 * @param cents - The amount in cents
 * @returns The amount in dollars as a number with 2 decimal places
 */
export function centsToDollars(cents: number): number {
  if (cents === undefined || cents === null || isNaN(cents)) {
    return 0;
  }

  // Convert cents to dollars with 2 decimal places
  return parseFloat((cents / 100).toFixed(2));
}

/**
 * Formats a currency value for display
 * @param amount - The amount in dollars
 * @param currency - The currency code (default: USD)
 * @returns Formatted currency string (e.g., "$10.00")
 */
export function formatCurrency(amount: number, currency: string = 'USD'): string {
  if (amount === undefined || amount === null || isNaN(amount)) {
    return '$0.00';
  }

  // Format the amount with 2 decimal places
  const formattedAmount = amount.toFixed(2);

  // Add the appropriate currency symbol
  switch (currency) {
    case 'USD':
      return `$${formattedAmount}`;
    case 'EUR':
      return `€${formattedAmount}`;
    case 'GBP':
      return `£${formattedAmount}`;
    default:
      return `${formattedAmount} ${currency}`;
  }
}

/**
 * Simple slugify function to convert text to URL-friendly slug
 * @param text - The text to slugify
 * @returns URL-friendly slug
 */
export function slugify(text: string): string {
  if (!text) return "";

  return text
    .toString()
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-')           // Replace spaces with -
    .replace(/[^\w\-]+/g, '')       // Remove all non-word chars
    .replace(/\-\-+/g, '-')         // Replace multiple - with single -
    .replace(/^-+/, '')             // Trim - from start of text
    .replace(/-+$/, '');            // Trim - from end of text
}

/**
 * Generate brand slug from website domain (text between www. and .com)
 * @param website - The website URL
 * @returns Brand slug extracted from domain
 */
export function generateBrandSlug(website: string): string {
  const websiteSlug = extractWebsiteSlug(website);
  return slugify(websiteSlug);
}

/**
 * Generate product slug from product title
 * @param title - The product title
 * @returns Product slug
 */
export function generateProductSlug(title: string): string {
  return slugify(title);
}
