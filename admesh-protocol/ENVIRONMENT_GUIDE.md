# AdMesh Protocol Environment Setup Guide

This guide provides a streamlined approach to managing environments in the AdMesh Protocol backend.

## 🌍 Available Environments

| Environment | API URL | Debug | Log Level | Use Case |
|-------------|---------|-------|-----------|----------|
| **Development** | `http://127.0.0.1:8000` | ✅ Enabled | DEBUG | Local development with detailed logging |
| **Production** | `https://api.useadmesh.com` | ❌ Disabled | WARNING | Live deployment |

## 🚀 Quick Start Commands

### Development (Most Common)
```bash
./run_dev.sh                  # Switch to dev environment and start with auto-reload
# OR manually:
python scripts/switch_env.py dev
python -m uvicorn api.main:app --reload --host 127.0.0.1 --port 8000
```

### Production Deployment
```bash
./run_prod.sh                 # Switch to production environment and start
# OR manually:
python scripts/switch_env.py prod
python -m uvicorn api.main:app --host 127.0.0.1 --port 8000
```

## 🔧 Environment Management

### Switch Environments Only
```bash
python scripts/switch_env.py dev     # Switch to development
python scripts/switch_env.py prod    # Switch to production
```

## 📁 Environment Files

- **`.env.dev`** - Development configuration
- **`.env.production`** - Production configuration
- **`.env`** - Active environment (auto-generated, don't edit manually)

## 🔍 Environment Variables

### Core Configuration
- `ENV` - Environment name (development/production)
- `DEBUG` - Debug mode flag
- `LOG_LEVEL` - Logging level (DEBUG/INFO/WARNING)
- `PORT` - Server port
- `SITE_URL` - API base URL

### Firebase Configuration
- `GOOGLE_APPLICATION_CREDENTIALS` - Firebase service account file path

### External Services
- `OPENROUTER_API_KEY` - OpenRouter API key
- `RESEND_API_KEY` - Resend email service API key
- `STRIPE_SECRET_KEY` - Stripe payment processing key
- `STRIPE_WEBHOOK_SECRET` - Stripe webhook secret

### Feature Flags
- `ENABLE_ANALYTICS` - Analytics tracking
- `ENABLE_RATE_LIMITING` - API rate limiting
- `ENABLE_CACHING` - Response caching
- `ENABLE_EMAIL_VERIFICATION` - Email verification
- `ENABLE_TRUST_SCORE_THROTTLING` - Trust score based throttling

## 🎯 Environment-Specific Settings

### Development
- Debug enabled for detailed logging
- All feature flags configurable
- Uses development Firebase project
- Auto-reload enabled

### Production
- All debugging disabled
- All feature flags enabled
- Uses production Firebase project
- Optimized for performance

## 🔄 Integration with Frontend

The backend environment should match the frontend environment:

| Backend | Frontend | API URL |
|---------|----------|---------|
| `dev` | `development` | `http://127.0.0.1:8000` |
| `prod` | `production` | `https://api.useadmesh.com` |

## 🐛 Troubleshooting

### Environment Not Switching
```bash
# Check current environment
grep "ENV=" .env

# Manually switch
python scripts/switch_env.py dev
```

### API Connection Issues
```bash
# Verify API URL in environment
grep "SITE_URL=" .env

# Test API connectivity
curl http://127.0.0.1:8000/health
```

### Debug Logging Not Working
```bash
# Check debug settings
grep "DEBUG=" .env
grep "LOG_LEVEL=" .env

# Ensure development environment
python scripts/switch_env.py dev
```
